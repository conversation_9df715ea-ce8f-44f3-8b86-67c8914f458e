{"name": "mwady-net", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@supabase/supabase-js": "^2.49.8", "cloudinary": "^2.6.1", "framer-motion": "^12.14.0", "lucide-react": "^0.511.0", "next": "15.3.2", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}