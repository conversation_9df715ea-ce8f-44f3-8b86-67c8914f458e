'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Menu, 
  X, 
  Search, 
  BookOpen, 
  GraduationCap, 
  Users, 
  Moon, 
  Sun,
  Globe,
  ChevronDown
} from 'lucide-react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [currentLang, setCurrentLang] = useState('ar')

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle('dark')
  }

  const toggleLanguage = () => {
    setCurrentLang(currentLang === 'ar' ? 'en' : 'ar')
    // Here you would implement actual language switching logic
  }

  const navItems = [
    {
      name: 'الرئيسية',
      nameEn: 'Home',
      href: '/',
      icon: BookOpen
    },
    {
      name: 'الاختبارات',
      nameEn: 'Exams',
      href: '/exams',
      icon: GraduationCap,
      submenu: [
        { name: 'اختبارات منتصف الفصل', nameEn: 'Midterm Exams', href: '/exams?type=midterm' },
        { name: 'اختبارات نهائية', nameEn: 'Final Exams', href: '/exams?type=final' },
        { name: 'جميع الاختبارات', nameEn: 'All Exams', href: '/exams' }
      ]
    },
    {
      name: 'المراحل التعليمية',
      nameEn: 'Education Levels',
      href: '/grades',
      icon: Users,
      submenu: [
        { name: 'المرحلة الابتدائية', nameEn: 'Primary', href: '/grades?level=primary' },
        { name: 'المرحلة المتوسطة', nameEn: 'Middle', href: '/grades?level=middle' },
        { name: 'المرحلة الثانوية', nameEn: 'High School', href: '/grades?level=high' }
      ]
    },
    {
      name: 'للمعلمين',
      nameEn: 'For Teachers',
      href: '/teachers',
      icon: Users
    }
  ]

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' 
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div className="hidden md:block">
              <h1 className="text-xl font-bold text-gray-800">
                موقع موادي
              </h1>
              <p className="text-xs text-gray-500 font-mono">mwady.net</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {navItems.map((item, index) => (
              <div key={index} className="relative group">
                <Link
                  href={item.href}
                  className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-lg transition-colors ${
                    isScrolled 
                      ? 'text-gray-700 hover:text-purple-600 hover:bg-purple-50' 
                      : 'text-white hover:text-purple-200 hover:bg-white/10'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{currentLang === 'ar' ? item.name : item.nameEn}</span>
                  {item.submenu && <ChevronDown className="w-4 h-4" />}
                </Link>

                {/* Submenu */}
                {item.submenu && (
                  <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="py-2">
                      {item.submenu.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          href={subItem.href}
                          className="block px-4 py-3 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors"
                        >
                          {currentLang === 'ar' ? subItem.name : subItem.nameEn}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Search Button */}
            <button 
              className={`p-2 rounded-lg transition-colors ${
                isScrolled 
                  ? 'text-gray-600 hover:text-purple-600 hover:bg-purple-50' 
                  : 'text-white hover:text-purple-200 hover:bg-white/10'
              }`}
            >
              <Search className="w-5 h-5" />
            </button>

            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className={`p-2 rounded-lg transition-colors ${
                isScrolled 
                  ? 'text-gray-600 hover:text-purple-600 hover:bg-purple-50' 
                  : 'text-white hover:text-purple-200 hover:bg-white/10'
              }`}
            >
              <Globe className="w-5 h-5" />
            </button>

            {/* Dark Mode Toggle */}
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-lg transition-colors ${
                isScrolled 
                  ? 'text-gray-600 hover:text-purple-600 hover:bg-purple-50' 
                  : 'text-white hover:text-purple-200 hover:bg-white/10'
              }`}
            >
              {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`lg:hidden p-2 rounded-lg transition-colors ${
                isScrolled 
                  ? 'text-gray-600 hover:text-purple-600 hover:bg-purple-50' 
                  : 'text-white hover:text-purple-200 hover:bg-white/10'
              }`}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden bg-white border-t border-gray-200 shadow-lg"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="space-y-2">
                {navItems.map((item, index) => (
                  <div key={index}>
                    <Link
                      href={item.href}
                      className="flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <item.icon className="w-5 h-5" />
                      <span>{currentLang === 'ar' ? item.name : item.nameEn}</span>
                    </Link>
                    
                    {/* Mobile Submenu */}
                    {item.submenu && (
                      <div className="mr-8 mt-2 space-y-1">
                        {item.submenu.map((subItem, subIndex) => (
                          <Link
                            key={subIndex}
                            href={subItem.href}
                            className="block px-4 py-2 text-sm text-gray-600 hover:text-purple-600 transition-colors"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            {currentLang === 'ar' ? subItem.name : subItem.nameEn}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Ad Space - Hidden by default, will be shown when ads are added */}
      <div className="hidden" id="header-ad-space">
        {/* This space will be used for AdSense ads later */}
        <div className="bg-gray-100 h-16 flex items-center justify-center text-gray-500 text-sm">
          مساحة إعلانية
        </div>
      </div>
    </header>
  )
}
