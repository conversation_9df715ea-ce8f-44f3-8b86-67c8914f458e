{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_ce960574.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"cairo_ce960574-module__O9vAua__className\",\n  \"variable\": \"cairo_ce960574-module__O9vAua__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_ce960574.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Cairo%22,%22arguments%22:[{%22subsets%22:[%22arabic%22],%22variable%22:%22--font-cairo%22}],%22variableName%22:%22cairo%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Cairo', 'Cairo Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/components/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/components/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/lib/seo-keywords.ts"], "sourcesContent": ["// SEO Keywords for Saudi Educational Content\nexport const seoKeywords = {\n  // Primary Keywords (High Volume)\n  primary: [\n    'اختبارات منتصف الفصل',\n    'اختبارات نهائية',\n    'نماذج اختبارات',\n    'بنك الاختبارات',\n    'اختبارات تجريبية',\n    'نماذج امتحانات',\n    'اختبارات السعودية',\n    'التعليم السعودي',\n    'مناهج السعودية',\n    'وزارة التعليم'\n  ],\n\n  // Secondary Keywords (Medium Volume)\n  secondary: [\n    'اختبارات ابتدائي',\n    'اختبارات متوسط', \n    'اختبارات ثانوي',\n    'اختبارات الفصل الأول',\n    'اختبارات الفصل الثاني',\n    'اختبارات الفصل الثالث',\n    'حلول الاختبارات',\n    'مراجعة نهائية',\n    'تحضير الاختبارات',\n    'دراسة الامتحانات'\n  ],\n\n  // Subject-Specific Keywords\n  subjects: [\n    'اختبارات رياضيات',\n    'اختبارات علوم',\n    'اختبارات لغتي',\n    'اختبارات انجليزي',\n    'اختبارات اجتماعيات',\n    'اختبارات دراسات اسلامية',\n    'اختبارات تجويد',\n    'اختبارات مهارات رقمية',\n    'اختبارات تربية فنية',\n    'اختبارات تربية بدنية'\n  ],\n\n  // Grade-Specific Keywords\n  grades: [\n    'اختبارات أول ابتدائي',\n    'اختبارات ثاني ابتدائي',\n    'اختبارات ثالث ابتدائي',\n    'اختبارات رابع ابتدائي',\n    'اختبارات خامس ابتدائي',\n    'اختبارات سادس ابتدائي',\n    'اختبارات أول متوسط',\n    'اختبارات ثاني متوسط',\n    'اختبارات ثالث متوسط',\n    'اختبارات أول ثانوي',\n    'اختبارات ثاني ثانوي',\n    'اختبارات ثالث ثانوي'\n  ],\n\n  // Long-tail Keywords (Low Competition, High Intent)\n  longTail: [\n    'تحميل اختبارات منتصف الفصل pdf',\n    'نماذج اختبارات نهائية مع الحلول',\n    'بنك اختبارات وزارة التعليم السعودية',\n    'اختبارات تجريبية للمرحلة الابتدائية',\n    'نماذج امتحانات المرحلة المتوسطة',\n    'اختبارات نهائية المرحلة الثانوية',\n    'مراجعة شاملة قبل الاختبارات',\n    'تحضير الطلاب للامتحانات النهائية',\n    'نصائح للنجاح في الاختبارات',\n    'استراتيجيات حل الاختبارات'\n  ],\n\n  // Trending Keywords (Social Media & YouTube)\n  trending: [\n    'اختبارات تيك توك',\n    'مراجعة يوتيوب',\n    'شرح الاختبارات',\n    'حل نماذج الامتحانات',\n    'تجميعات الاختبارات',\n    'ملخصات المراجعة',\n    'خرائط ذهنية',\n    'فلاش كاردز',\n    'مذاكرة جماعية',\n    'دراسة اونلاين'\n  ],\n\n  // Location-Based Keywords\n  locations: [\n    'اختبارات الرياض',\n    'اختبارات جدة',\n    'اختبارات الدمام',\n    'اختبارات مكة',\n    'اختبارات المدينة',\n    'اختبارات الطائف',\n    'اختبارات القصيم',\n    'اختبارات حائل',\n    'اختبارات جازان',\n    'اختبارات نجران'\n  ],\n\n  // Exam Types\n  examTypes: [\n    'اختبار شفهي',\n    'اختبار كتابي',\n    'اختبار عملي',\n    'اختبار الكتروني',\n    'اختبار تفاعلي',\n    'اختبار محوسب',\n    'اختبار ورقي',\n    'اختبار مركزي',\n    'اختبار موحد',\n    'اختبار تشخيصي'\n  ],\n\n  // Academic Year Keywords\n  academicYear: [\n    'العام الدراسي 1446',\n    'الفصل الدراسي الأول 1446',\n    'الفصل الدراسي الثاني 1446', \n    'الفصل الدراسي الثالث 1446',\n    'منهج جديد 1446',\n    'تحديث المناهج',\n    'طبعة جديدة',\n    'منهج محدث',\n    'كتب جديدة',\n    'مقررات حديثة'\n  ]\n}\n\n// Generate meta keywords for specific pages\nexport const generateMetaKeywords = (\n  grade?: string,\n  subject?: string,\n  examType?: 'midterm' | 'final',\n  semester?: number\n) => {\n  let keywords = [...seoKeywords.primary]\n\n  if (grade) {\n    keywords.push(`اختبارات ${grade}`)\n  }\n\n  if (subject) {\n    keywords.push(`اختبارات ${subject}`)\n  }\n\n  if (examType === 'midterm') {\n    keywords.push('اختبارات منتصف الفصل')\n  } else if (examType === 'final') {\n    keywords.push('اختبارات نهائية')\n  }\n\n  if (semester) {\n    keywords.push(`الفصل الدراسي ${semester === 1 ? 'الأول' : semester === 2 ? 'الثاني' : 'الثالث'}`)\n  }\n\n  // Add some secondary and long-tail keywords\n  keywords.push(...seoKeywords.secondary.slice(0, 5))\n  keywords.push(...seoKeywords.longTail.slice(0, 3))\n\n  return keywords.join(', ')\n}\n\n// Generate structured data for SEO\nexport const generateStructuredData = (\n  title: string,\n  description: string,\n  url: string,\n  imageUrl?: string\n) => {\n  return {\n    '@context': 'https://schema.org',\n    '@type': 'EducationalOrganization',\n    name: 'موقع موادي - mwady.net',\n    description: 'الموقع الأول لتحضير الاختبارات في التعليم السعودي',\n    url: 'https://mwady.net',\n    logo: 'https://mwady.net/logo.png',\n    sameAs: [\n      'https://twitter.com/mwady_net',\n      'https://facebook.com/mwady.net',\n      'https://instagram.com/mwady_net'\n    ],\n    mainEntity: {\n      '@type': 'WebPage',\n      name: title,\n      description: description,\n      url: url,\n      image: imageUrl,\n      publisher: {\n        '@type': 'Organization',\n        name: 'موقع موادي',\n        logo: 'https://mwady.net/logo.png'\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;AACtC,MAAM,cAAc;IACzB,iCAAiC;IACjC,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qCAAqC;IACrC,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4BAA4B;IAC5B,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,0BAA0B;IAC1B,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,oDAAoD;IACpD,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,6CAA6C;IAC7C,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,0BAA0B;IAC1B,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,aAAa;IACb,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,yBAAyB;IACzB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,uBAAuB,CAClC,OACA,SACA,UACA;IAEA,IAAI,WAAW;WAAI,YAAY,OAAO;KAAC;IAEvC,IAAI,OAAO;QACT,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO;IACnC;IAEA,IAAI,SAAS;QACX,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS;IACrC;IAEA,IAAI,aAAa,WAAW;QAC1B,SAAS,IAAI,CAAC;IAChB,OAAO,IAAI,aAAa,SAAS;QAC/B,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,UAAU;QACZ,SAAS,IAAI,CAAC,CAAC,cAAc,EAAE,aAAa,IAAI,UAAU,aAAa,IAAI,WAAW,UAAU;IAClG;IAEA,4CAA4C;IAC5C,SAAS,IAAI,IAAI,YAAY,SAAS,CAAC,KAAK,CAAC,GAAG;IAChD,SAAS,IAAI,IAAI,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG;IAE/C,OAAO,SAAS,IAAI,CAAC;AACvB;AAGO,MAAM,yBAAyB,CACpC,OACA,aACA,KACA;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aAAa;QACb,KAAK;QACL,MAAM;QACN,QAAQ;YACN;YACA;YACA;SACD;QACD,YAAY;YACV,SAAS;YACT,MAAM;YACN,aAAa;YACb,KAAK;YACL,OAAO;YACP,WAAW;gBACT,SAAS;gBACT,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter, Cairo } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport { generateMetaKeywords, generateStructuredData } from \"@/lib/seo-keywords\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nconst cairo = Cairo({\n  subsets: [\"arabic\"],\n  variable: \"--font-cairo\",\n});\n\nexport const metadata: Metadata = {\n  title: \"موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي\",\n  description: \"منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية - اختبارات منتصف الفصل والاختبارات النهائية للتعليم السعودي\",\n  keywords: generateMetaKeywords(),\n  authors: [{ name: \"موقع موادي\" }],\n  creator: \"موقع موادي\",\n  publisher: \"موقع موادي\",\n  robots: \"index, follow\",\n  openGraph: {\n    title: \"موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي\",\n    description: \"منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية\",\n    url: \"https://mwady.net\",\n    siteName: \"موقع موادي\",\n    locale: \"ar_SA\",\n    type: \"website\",\n    images: [\n      {\n        url: \"https://mwady.net/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"موقع موادي - اختبارات التعليم السعودي\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي\",\n    description: \"منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية\",\n    images: [\"https://mwady.net/og-image.jpg\"],\n  },\n  alternates: {\n    canonical: \"https://mwady.net\",\n    languages: {\n      \"ar-SA\": \"https://mwady.net\",\n      \"en-US\": \"https://mwady.net/en\",\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const structuredData = generateStructuredData(\n    \"موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي\",\n    \"منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية\",\n    \"https://mwady.net\"\n  );\n\n  return (\n    <html lang=\"ar\" dir=\"rtl\">\n      <head>\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n        />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\n        <meta name=\"theme-color\" content=\"#7c3aed\" />\n        <meta name=\"msapplication-TileColor\" content=\"#7c3aed\" />\n      </head>\n      <body className={`${inter.variable} ${cairo.variable} font-cairo antialiased`}>\n        <Header />\n        <main>{children}</main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD;IAC7B,SAAS;QAAC;YAAE,MAAM;QAAa;KAAE;IACjC,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAiC;IAC5C;IACA,YAAY;QACV,WAAW;QACX,WAAW;YACT,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAC1C,kEACA,+DACA;IAGF,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;;0BAClB,8OAAC;;kCACC,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BAAE,QAAQ,KAAK,SAAS,CAAC;wBAAgB;;;;;;kCAEpE,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;;;;;;;0BAE/C,8OAAC;gBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,uBAAuB,CAAC;;kCAC3E,8OAAC,4HAAA,CAAA,UAAM;;;;;kCACP,8OAAC;kCAAM;;;;;;kCACP,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}