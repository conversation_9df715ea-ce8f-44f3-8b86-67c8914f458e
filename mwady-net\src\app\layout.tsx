import type { Metadata } from "next";
import { Inter, Cairo } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { generateMetaKeywords, generateStructuredData } from "@/lib/seo-keywords";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const cairo = Cairo({
  subsets: ["arabic"],
  variable: "--font-cairo",
});

export const metadata: Metadata = {
  title: "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
  description: "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية - اختبارات منتصف الفصل والاختبارات النهائية للتعليم السعودي",
  keywords: generateMetaKeywords(),
  authors: [{ name: "موقع موادي" }],
  creator: "موقع موادي",
  publisher: "موقع موادي",
  robots: "index, follow",
  openGraph: {
    title: "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
    description: "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية",
    url: "https://mwady.net",
    siteName: "موقع موادي",
    locale: "ar_SA",
    type: "website",
    images: [
      {
        url: "https://mwady.net/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "موقع موادي - اختبارات التعليم السعودي",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
    description: "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية",
    images: ["https://mwady.net/og-image.jpg"],
  },
  alternates: {
    canonical: "https://mwady.net",
    languages: {
      "ar-SA": "https://mwady.net",
      "en-US": "https://mwady.net/en",
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const structuredData = generateStructuredData(
    "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
    "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية",
    "https://mwady.net"
  );

  return (
    <html lang="ar" dir="rtl">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#0284c7" />
        <meta name="msapplication-TileColor" content="#0284c7" />
      </head>
      <body className={`${inter.variable} ${cairo.variable} font-cairo antialiased`}>
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
