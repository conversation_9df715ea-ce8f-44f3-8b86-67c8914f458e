import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Grade {
  id: string
  name_ar: string
  name_en: string
  level: 'primary' | 'middle' | 'high'
  order: number
  created_at: string
}

export interface Subject {
  id: string
  name_ar: string
  name_en: string
  grade_id: string
  icon?: string
  color?: string
  created_at: string
}

export interface Exam {
  id: string
  title_ar: string
  title_en: string
  description_ar?: string
  description_en?: string
  subject_id: string
  grade_id: string
  semester: 1 | 2 | 3
  exam_type: 'midterm' | 'final'
  file_url: string
  file_size: number
  download_count: number
  is_active: boolean
  created_by?: string
  created_at: string
  updated_at: string
}

export interface Teacher {
  id: string
  name: string
  email: string
  phone?: string
  school?: string
  is_verified: boolean
  created_at: string
}

// Database Functions
export const dbFunctions = {
  // Grades
  async getGrades() {
    const { data, error } = await supabase
      .from('grades')
      .select('*')
      .order('order')
    
    if (error) throw error
    return data as Grade[]
  },

  // Subjects
  async getSubjectsByGrade(gradeId: string) {
    const { data, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('grade_id', gradeId)
      .order('name_ar')
    
    if (error) throw error
    return data as Subject[]
  },

  // Exams
  async getExams(filters?: {
    gradeId?: string
    subjectId?: string
    semester?: number
    examType?: 'midterm' | 'final'
    limit?: number
  }) {
    let query = supabase
      .from('exams')
      .select(`
        *,
        subjects:subject_id(name_ar, name_en),
        grades:grade_id(name_ar, name_en)
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (filters?.gradeId) {
      query = query.eq('grade_id', filters.gradeId)
    }
    
    if (filters?.subjectId) {
      query = query.eq('subject_id', filters.subjectId)
    }
    
    if (filters?.semester) {
      query = query.eq('semester', filters.semester)
    }
    
    if (filters?.examType) {
      query = query.eq('exam_type', filters.examType)
    }
    
    if (filters?.limit) {
      query = query.limit(filters.limit)
    }

    const { data, error } = await query
    
    if (error) throw error
    return data as (Exam & {
      subjects: Pick<Subject, 'name_ar' | 'name_en'>
      grades: Pick<Grade, 'name_ar' | 'name_en'>
    })[]
  },

  async incrementDownloadCount(examId: string) {
    const { error } = await supabase.rpc('increment_download_count', {
      exam_id: examId
    })
    
    if (error) throw error
  },

  // Teachers
  async createTeacher(teacher: Omit<Teacher, 'id' | 'created_at' | 'is_verified'>) {
    const { data, error } = await supabase
      .from('teachers')
      .insert([{ ...teacher, is_verified: false }])
      .select()
      .single()
    
    if (error) throw error
    return data as Teacher
  },

  async createExam(exam: Omit<Exam, 'id' | 'created_at' | 'updated_at' | 'download_count'>) {
    const { data, error } = await supabase
      .from('exams')
      .insert([{ ...exam, download_count: 0 }])
      .select()
      .single()
    
    if (error) throw error
    return data as Exam
  }
}
