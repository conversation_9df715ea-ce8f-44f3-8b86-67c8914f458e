import { v2 as cloudinary } from 'cloudinary'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

export { cloudinary }

// Helper functions for file uploads
export const uploadToCloudinary = async (
  file: File,
  folder: string = 'mwady-exams'
): Promise<{
  url: string
  publicId: string
  size: number
}> => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('upload_preset', 'mwady_preset') // You'll need to create this in Cloudinary
  formData.append('folder', folder)

  const response = await fetch(
    `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/auto/upload`,
    {
      method: 'POST',
      body: formData,
    }
  )

  if (!response.ok) {
    throw new Error('Failed to upload file to Cloudinary')
  }

  const data = await response.json()
  
  return {
    url: data.secure_url,
    publicId: data.public_id,
    size: data.bytes,
  }
}

// Generate optimized URLs for different use cases
export const getOptimizedImageUrl = (
  publicId: string,
  options: {
    width?: number
    height?: number
    quality?: 'auto' | number
    format?: 'auto' | 'webp' | 'jpg' | 'png'
  } = {}
) => {
  const {
    width = 800,
    height,
    quality = 'auto',
    format = 'auto'
  } = options

  let transformation = `w_${width}`
  
  if (height) {
    transformation += `,h_${height},c_fill`
  }
  
  transformation += `,q_${quality},f_${format}`

  return `https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload/${transformation}/${publicId}`
}

// Get PDF thumbnail
export const getPdfThumbnail = (publicId: string) => {
  return `https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload/w_300,h_400,c_fill,f_jpg,pg_1/${publicId}.jpg`
}

// Delete file from Cloudinary
export const deleteFromCloudinary = async (publicId: string) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId)
    return result
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error)
    throw error
  }
}
