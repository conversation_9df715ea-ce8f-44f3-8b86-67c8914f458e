// SEO Keywords for Saudi Educational Content
export const seoKeywords = {
  // Primary Keywords (High Volume)
  primary: [
    'اختبارات منتصف الفصل',
    'اختبارات نهائية',
    'نماذج اختبارات',
    'بنك الاختبارات',
    'اختبارات تجريبية',
    'نماذج امتحانات',
    'اختبارات السعودية',
    'التعليم السعودي',
    'مناهج السعودية',
    'وزارة التعليم'
  ],

  // Secondary Keywords (Medium Volume)
  secondary: [
    'اختبارات ابتدائي',
    'اختبارات متوسط', 
    'اختبارات ثانوي',
    'اختبارات الفصل الأول',
    'اختبارات الفصل الثاني',
    'اختبارات الفصل الثالث',
    'حلول الاختبارات',
    'مراجعة نهائية',
    'تحضير الاختبارات',
    'دراسة الامتحانات'
  ],

  // Subject-Specific Keywords
  subjects: [
    'اختبارات رياضيات',
    'اختبارات علوم',
    'اختبارات لغتي',
    'اختبارات انجليزي',
    'اختبارات اجتماعيات',
    'اختبارات دراسات اسلامية',
    'اختبارات تجويد',
    'اختبارات مهارات رقمية',
    'اختبارات تربية فنية',
    'اختبارات تربية بدنية'
  ],

  // Grade-Specific Keywords
  grades: [
    'اختبارات أول ابتدائي',
    'اختبارات ثاني ابتدائي',
    'اختبارات ثالث ابتدائي',
    'اختبارات رابع ابتدائي',
    'اختبارات خامس ابتدائي',
    'اختبارات سادس ابتدائي',
    'اختبارات أول متوسط',
    'اختبارات ثاني متوسط',
    'اختبارات ثالث متوسط',
    'اختبارات أول ثانوي',
    'اختبارات ثاني ثانوي',
    'اختبارات ثالث ثانوي'
  ],

  // Long-tail Keywords (Low Competition, High Intent)
  longTail: [
    'تحميل اختبارات منتصف الفصل pdf',
    'نماذج اختبارات نهائية مع الحلول',
    'بنك اختبارات وزارة التعليم السعودية',
    'اختبارات تجريبية للمرحلة الابتدائية',
    'نماذج امتحانات المرحلة المتوسطة',
    'اختبارات نهائية المرحلة الثانوية',
    'مراجعة شاملة قبل الاختبارات',
    'تحضير الطلاب للامتحانات النهائية',
    'نصائح للنجاح في الاختبارات',
    'استراتيجيات حل الاختبارات'
  ],

  // Trending Keywords (Social Media & YouTube)
  trending: [
    'اختبارات تيك توك',
    'مراجعة يوتيوب',
    'شرح الاختبارات',
    'حل نماذج الامتحانات',
    'تجميعات الاختبارات',
    'ملخصات المراجعة',
    'خرائط ذهنية',
    'فلاش كاردز',
    'مذاكرة جماعية',
    'دراسة اونلاين'
  ],

  // Location-Based Keywords
  locations: [
    'اختبارات الرياض',
    'اختبارات جدة',
    'اختبارات الدمام',
    'اختبارات مكة',
    'اختبارات المدينة',
    'اختبارات الطائف',
    'اختبارات القصيم',
    'اختبارات حائل',
    'اختبارات جازان',
    'اختبارات نجران'
  ],

  // Exam Types
  examTypes: [
    'اختبار شفهي',
    'اختبار كتابي',
    'اختبار عملي',
    'اختبار الكتروني',
    'اختبار تفاعلي',
    'اختبار محوسب',
    'اختبار ورقي',
    'اختبار مركزي',
    'اختبار موحد',
    'اختبار تشخيصي'
  ],

  // Academic Year Keywords
  academicYear: [
    'العام الدراسي 1446',
    'الفصل الدراسي الأول 1446',
    'الفصل الدراسي الثاني 1446', 
    'الفصل الدراسي الثالث 1446',
    'منهج جديد 1446',
    'تحديث المناهج',
    'طبعة جديدة',
    'منهج محدث',
    'كتب جديدة',
    'مقررات حديثة'
  ]
}

// Generate meta keywords for specific pages
export const generateMetaKeywords = (
  grade?: string,
  subject?: string,
  examType?: 'midterm' | 'final',
  semester?: number
) => {
  let keywords = [...seoKeywords.primary]

  if (grade) {
    keywords.push(`اختبارات ${grade}`)
  }

  if (subject) {
    keywords.push(`اختبارات ${subject}`)
  }

  if (examType === 'midterm') {
    keywords.push('اختبارات منتصف الفصل')
  } else if (examType === 'final') {
    keywords.push('اختبارات نهائية')
  }

  if (semester) {
    keywords.push(`الفصل الدراسي ${semester === 1 ? 'الأول' : semester === 2 ? 'الثاني' : 'الثالث'}`)
  }

  // Add some secondary and long-tail keywords
  keywords.push(...seoKeywords.secondary.slice(0, 5))
  keywords.push(...seoKeywords.longTail.slice(0, 3))

  return keywords.join(', ')
}

// Generate structured data for SEO
export const generateStructuredData = (
  title: string,
  description: string,
  url: string,
  imageUrl?: string
) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'EducationalOrganization',
    name: 'موقع موادي - mwady.net',
    description: 'الموقع الأول لتحضير الاختبارات في التعليم السعودي',
    url: 'https://mwady.net',
    logo: 'https://mwady.net/logo.png',
    sameAs: [
      'https://twitter.com/mwady_net',
      'https://facebook.com/mwady.net',
      'https://instagram.com/mwady_net'
    ],
    mainEntity: {
      '@type': 'WebPage',
      name: title,
      description: description,
      url: url,
      image: imageUrl,
      publisher: {
        '@type': 'Organization',
        name: 'موقع موادي',
        logo: 'https://mwady.net/logo.png'
      }
    }
  }
}
