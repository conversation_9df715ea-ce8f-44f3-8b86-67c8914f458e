/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@import "tailwindcss";

@theme {
  --font-cairo: 'Cairo', sans-serif;
  --font-inter: 'Inter', sans-serif;

  /* Professional Blue-Green Color Palette */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* Complementary Teal Colors */
  --color-secondary-50: #f0fdfa;
  --color-secondary-100: #ccfbf1;
  --color-secondary-200: #99f6e4;
  --color-secondary-300: #5eead4;
  --color-secondary-400: #2dd4bf;
  --color-secondary-500: #14b8a6;
  --color-secondary-600: #0d9488;
  --color-secondary-700: #0f766e;
  --color-secondary-800: #115e59;
  --color-secondary-900: #134e4a;

  /* Accent Colors */
  --color-accent-50: #fefce8;
  --color-accent-100: #fef9c3;
  --color-accent-200: #fef08a;
  --color-accent-300: #fde047;
  --color-accent-400: #facc15;
  --color-accent-500: #eab308;
  --color-accent-600: #ca8a04;
  --color-accent-700: #a16207;
  --color-accent-800: #854d0e;
  --color-accent-900: #713f12;

  /* Success Colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
}

/* Base Styles */
body {
  font-family: var(--font-cairo), var(--font-inter), sans-serif;
  direction: rtl;
}
