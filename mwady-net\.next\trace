[{"name": "hot-reloader", "duration": 118, "timestamp": 129364080993, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748286396038, "traceId": "7032628da38c2175"}, {"name": "setup-dev-bundler", "duration": 2056858, "timestamp": 129363526381, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748286395484, "traceId": "7032628da38c2175"}, {"name": "run-instrumentation-hook", "duration": 47, "timestamp": 129365803117, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748286397760, "traceId": "7032628da38c2175"}, {"name": "start-dev-server", "duration": 3776561, "timestamp": 129362064573, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "452456448", "memory.totalMem": "4183605248", "memory.heapSizeLimit": "2141192192", "memory.rss": "170008576", "memory.heapTotal": "97189888", "memory.heapUsed": "70168016"}, "startTime": 1748286394022, "traceId": "7032628da38c2175"}, {"name": "ensure-page", "duration": 1435, "timestamp": 129420718581, "id": 5, "parentId": 3, "tags": {"inputPage": "/"}, "startTime": 1748286452676, "traceId": "7032628da38c2175"}, {"name": "ensure-page", "duration": 393, "timestamp": 129420720110, "id": 6, "parentId": 3, "tags": {"inputPage": "/"}, "startTime": 1748286452677, "traceId": "7032628da38c2175"}, {"name": "ensure-page", "duration": 429, "timestamp": 129420721460, "id": 7, "parentId": 3, "tags": {"inputPage": "/"}, "startTime": 1748286452679, "traceId": "7032628da38c2175"}, {"name": "ensure-page", "duration": 287, "timestamp": 129420722192, "id": 8, "parentId": 3, "tags": {"inputPage": "/"}, "startTime": 1748286452679, "traceId": "7032628da38c2175"}, {"name": "compile-path", "duration": 3809517, "timestamp": 129420733935, "id": 11, "tags": {"trigger": "/_not-found/page"}, "startTime": 1748286452691, "traceId": "7032628da38c2175"}, {"name": "ensure-page", "duration": 3813038, "timestamp": 129420731734, "id": 10, "parentId": 3, "tags": {"inputPage": "/_not-found/page"}, "startTime": 1748286452689, "traceId": "7032628da38c2175"}]