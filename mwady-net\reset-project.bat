@echo off
chcp 65001 >nul
title إعادة تعيين مشروع موادي

echo.
echo ========================================
echo    إعادة تعيين مشروع موادي
echo    حل مشاكل المكتبات والتبعيات
echo ========================================
echo.

echo ⚠️  تحذير: سيتم حذف جميع المكتبات المثبتة وإعادة تثبيتها
echo هذا قد يستغرق عدة دقائق...
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo تم الإلغاء.
    pause
    exit /b 0
)

echo.
echo [1/4] حذف المكتبات القديمة...
if exist "node_modules" (
    echo 🗑️ حذف مجلد node_modules...
    rmdir /s /q "node_modules"
    echo ✅ تم حذف node_modules
) else (
    echo ✅ node_modules غير موجود
)

echo [2/4] حذف ملفات التخزين المؤقت...
if exist "package-lock.json" (
    echo 🗑️ حذف package-lock.json...
    del "package-lock.json"
    echo ✅ تم حذف package-lock.json
)

if exist ".next" (
    echo 🗑️ حذف مجلد .next...
    rmdir /s /q ".next"
    echo ✅ تم حذف .next
)

echo [3/4] تنظيف تخزين npm المؤقت...
npm cache clean --force
echo ✅ تم تنظيف التخزين المؤقت

echo [4/4] إعادة تثبيت المكتبات...
echo 📦 تثبيت المكتبات الجديدة...
npm install
if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت المكتبات!
    echo.
    echo جرب الحلول التالية:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. شغل الملف كمدير
    echo 3. استخدم VPN إذا كان هناك حجب
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إعادة تعيين المشروع بنجاح!
echo.
echo يمكنك الآن تشغيل الموقع باستخدام:
echo start-mwady.bat
echo.
pause
