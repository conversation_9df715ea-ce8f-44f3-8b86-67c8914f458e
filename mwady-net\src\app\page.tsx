'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Search, BookOpen, Download, Users, Star, TrendingUp, GraduationCap, FileText, Clock, Award } from 'lucide-react'
import Link from 'next/link'
import { dbFunctions, Grade, Exam } from '@/lib/supabase'
import { generateMetaKeywords } from '@/lib/seo-keywords'

export default function Home() {
  const [grades, setGrades] = useState<Grade[]>([])
  const [recentExams, setRecentExams] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // For now, we'll use mock data since database isn't set up yet
      const mockGrades = [
        { id: '1', name_ar: 'الأول الابتدائي', name_en: 'First Grade', level: 'primary' as const, order: 1, created_at: '' },
        { id: '2', name_ar: 'الثاني الابتدائي', name_en: 'Second Grade', level: 'primary' as const, order: 2, created_at: '' },
        { id: '3', name_ar: 'الثالث الابتدائي', name_en: 'Third Grade', level: 'primary' as const, order: 3, created_at: '' },
        { id: '4', name_ar: 'الرابع الابتدائي', name_en: 'Fourth Grade', level: 'primary' as const, order: 4, created_at: '' },
        { id: '5', name_ar: 'الخامس الابتدائي', name_en: 'Fifth Grade', level: 'primary' as const, order: 5, created_at: '' },
        { id: '6', name_ar: 'السادس الابتدائي', name_en: 'Sixth Grade', level: 'primary' as const, order: 6, created_at: '' },
        { id: '7', name_ar: 'الأول المتوسط', name_en: 'Seventh Grade', level: 'middle' as const, order: 7, created_at: '' },
        { id: '8', name_ar: 'الثاني المتوسط', name_en: 'Eighth Grade', level: 'middle' as const, order: 8, created_at: '' },
        { id: '9', name_ar: 'الثالث المتوسط', name_en: 'Ninth Grade', level: 'middle' as const, order: 9, created_at: '' },
        { id: '10', name_ar: 'الأول الثانوي', name_en: 'Tenth Grade', level: 'high' as const, order: 10, created_at: '' },
        { id: '11', name_ar: 'الثاني الثانوي', name_en: 'Eleventh Grade', level: 'high' as const, order: 11, created_at: '' },
        { id: '12', name_ar: 'الثالث الثانوي', name_en: 'Twelfth Grade', level: 'high' as const, order: 12, created_at: '' }
      ]

      setGrades(mockGrades)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const stats = [
    { icon: BookOpen, label: 'اختبار متاح', value: '2,500+', color: 'text-blue-600' },
    { icon: Download, label: 'تحميل شهري', value: '50,000+', color: 'text-green-600' },
    { icon: Users, label: 'طالب مستفيد', value: '25,000+', color: 'text-purple-600' },
    { icon: Star, label: 'تقييم المستخدمين', value: '4.9/5', color: 'text-yellow-600' }
  ]

  const examTypes = [
    {
      title: 'اختبارات منتصف الفصل',
      description: 'نماذج اختبارات منتصف الفصل لجميع المواد والصفوف',
      icon: Clock,
      color: 'from-blue-500 to-blue-600',
      count: '1,200+'
    },
    {
      title: 'اختبارات نهائية',
      description: 'اختبارات نهائية شاملة مع نماذج الإجابة',
      icon: Award,
      color: 'from-purple-500 to-purple-600',
      count: '1,300+'
    }
  ]

  const educationLevels = [
    {
      title: 'المرحلة الابتدائية',
      description: 'من الصف الأول إلى السادس الابتدائي',
      icon: GraduationCap,
      color: 'from-green-500 to-green-600',
      grades: grades.filter(g => g.level === 'primary')
    },
    {
      title: 'المرحلة المتوسطة',
      description: 'من الصف الأول إلى الثالث المتوسط',
      icon: BookOpen,
      color: 'from-orange-500 to-orange-600',
      grades: grades.filter(g => g.level === 'middle')
    },
    {
      title: 'المرحلة الثانوية',
      description: 'من الصف الأول إلى الثالث الثانوي',
      icon: FileText,
      color: 'from-red-500 to-red-600',
      grades: grades.filter(g => g.level === 'high')
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white"
          >
            <div className="item-title mb-8">
              <h2 className="text-4xl md:text-6xl font-bold mb-4" style={{direction: 'rtl'}}>
                موقع موادي - <span style={{fontFamily: 'Roboto,sans-serif', fontWeight: 400}}>mwady.net</span>
              </h2>
              <h1 className="text-2xl md:text-3xl font-semibold text-blue-100">
                الموقع الأول لتحضير الاختبارات في التعليم السعودي
              </h1>
            </div>

            <p className="text-xl mb-8 max-w-3xl mx-auto leading-relaxed">
              منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية - اختبارات منتصف الفصل والاختبارات النهائية
            </p>

            {/* Search Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="max-w-2xl mx-auto mb-8"
            >
              <div className="relative">
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="ابحث عن اختبارات حسب المادة أو الصف..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-12 py-4 rounded-2xl text-gray-800 text-lg focus:outline-none focus:ring-4 focus:ring-white/30 shadow-xl"
                  dir="rtl"
                />
              </div>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
            >
              {stats.map((stat, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-2`} />
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <div className="text-sm text-blue-100">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Exam Types Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-800 mb-4">أنواع الاختبارات</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نوفر لك جميع أنواع الاختبارات التي تحتاجها للتحضير المثالي
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {examTypes.map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${type.color} p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105`}
              >
                <div className="relative z-10">
                  <type.icon className="w-12 h-12 mb-4" />
                  <h3 className="text-2xl font-bold mb-2">{type.title}</h3>
                  <p className="text-white/90 mb-4">{type.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-3xl font-bold">{type.count}</span>
                    <Link
                      href={`/exams?type=${type.title.includes('منتصف') ? 'midterm' : 'final'}`}
                      className="bg-white/20 hover:bg-white/30 px-6 py-2 rounded-full transition-colors"
                    >
                      تصفح الآن
                    </Link>
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Education Levels Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-800 mb-4">المراحل التعليمية</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              اختبارات شاملة لجميع المراحل التعليمية في المملكة العربية السعودية
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {educationLevels.map((level, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              >
                <div className={`bg-gradient-to-r ${level.color} p-6 text-white`}>
                  <level.icon className="w-12 h-12 mb-4" />
                  <h3 className="text-2xl font-bold mb-2">{level.title}</h3>
                  <p className="text-white/90">{level.description}</p>
                </div>

                <div className="p-6">
                  <div className="space-y-3">
                    {level.grades.map((grade) => (
                      <Link
                        key={grade.id}
                        href={`/grades/${grade.id}`}
                        className="block p-3 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 hover:border-gray-200"
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-800">{grade.name_ar}</span>
                          <span className="text-gray-400">→</span>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-800 mb-4">لماذا موقع موادي؟</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نحن نقدم أفضل تجربة تعليمية للطلاب والمعلمين في المملكة
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Download,
                title: 'تحميل مجاني',
                description: 'جميع الاختبارات متاحة للتحميل المجاني بصيغة PDF'
              },
              {
                icon: TrendingUp,
                title: 'محتوى محدث',
                description: 'نماذج حديثة ومحدثة وفقاً لأحدث المناهج السعودية'
              },
              {
                icon: Users,
                title: 'مجتمع تعليمي',
                description: 'انضم لآلاف الطلاب والمعلمين في رحلة التعلم'
              },
              {
                icon: Star,
                title: 'جودة عالية',
                description: 'اختبارات معدة من قبل معلمين متخصصين ومراجعة'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="text-center p-6 rounded-xl hover:bg-gray-50 transition-colors"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-white"
          >
            <h2 className="text-4xl font-bold mb-4">ابدأ رحلة التفوق الآن</h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              انضم لآلاف الطلاب الذين حققوا التفوق باستخدام منصة موادي
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/exams"
                className="bg-white text-purple-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-colors"
              >
                تصفح الاختبارات
              </Link>
              <Link
                href="/teachers/register"
                className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-purple-600 transition-colors"
              >
                انضم كمعلم
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
