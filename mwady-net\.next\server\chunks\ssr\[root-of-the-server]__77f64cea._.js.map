{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport {\n  Menu,\n  X,\n  Search,\n  BookOpen,\n  GraduationCap,\n  Users,\n  Moon,\n  Sun,\n  Globe,\n  ChevronDown\n} from 'lucide-react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isDarkMode, setIsDarkMode] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [currentLang, setCurrentLang] = useState('ar')\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const toggleDarkMode = () => {\n    setIsDarkMode(!isDarkMode)\n    document.documentElement.classList.toggle('dark')\n  }\n\n  const toggleLanguage = () => {\n    setCurrentLang(currentLang === 'ar' ? 'en' : 'ar')\n    // Here you would implement actual language switching logic\n  }\n\n  const navItems = [\n    {\n      name: 'الرئيسية',\n      nameEn: 'Home',\n      href: '/',\n      icon: BookOpen\n    },\n    {\n      name: 'الاختبارات',\n      nameEn: 'Exams',\n      href: '/exams',\n      icon: GraduationCap,\n      submenu: [\n        { name: 'اختبارات منتصف الفصل', nameEn: 'Midterm Exams', href: '/exams?type=midterm' },\n        { name: 'اختبارات نهائية', nameEn: 'Final Exams', href: '/exams?type=final' },\n        { name: 'جميع الاختبارات', nameEn: 'All Exams', href: '/exams' }\n      ]\n    },\n    {\n      name: 'المراحل التعليمية',\n      nameEn: 'Education Levels',\n      href: '/grades',\n      icon: Users,\n      submenu: [\n        { name: 'المرحلة الابتدائية', nameEn: 'Primary', href: '/grades?level=primary' },\n        { name: 'المرحلة المتوسطة', nameEn: 'Middle', href: '/grades?level=middle' },\n        { name: 'المرحلة الثانوية', nameEn: 'High School', href: '/grades?level=high' }\n      ]\n    },\n    {\n      name: 'للمعلمين',\n      nameEn: 'For Teachers',\n      href: '/teachers',\n      icon: Users\n    }\n  ]\n\n  return (\n    <header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled\n          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200'\n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-sky-600 to-teal-600 rounded-lg flex items-center justify-center\">\n              <BookOpen className=\"w-6 h-6 text-white\" />\n            </div>\n            <div className=\"hidden md:block\">\n              <h1 className=\"text-xl font-bold text-gray-800\">\n                موقع موادي\n              </h1>\n              <p className=\"text-xs text-gray-500 font-mono\">mwady.net</p>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8 space-x-reverse\">\n            {navItems.map((item, index) => (\n              <div key={index} className=\"relative group\">\n                <Link\n                  href={item.href}\n                  className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-lg transition-colors ${\n                    isScrolled\n                      ? 'text-gray-700 hover:text-sky-600 hover:bg-sky-50'\n                      : 'text-white hover:text-sky-200 hover:bg-white/10'\n                  }`}\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span>{currentLang === 'ar' ? item.name : item.nameEn}</span>\n                  {item.submenu && <ChevronDown className=\"w-4 h-4\" />}\n                </Link>\n\n                {/* Submenu */}\n                {item.submenu && (\n                  <div className=\"absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                    <div className=\"py-2\">\n                      {item.submenu.map((subItem, subIndex) => (\n                        <Link\n                          key={subIndex}\n                          href={subItem.href}\n                          className=\"block px-4 py-3 text-gray-700 hover:bg-sky-50 hover:text-sky-600 transition-colors\"\n                        >\n                          {currentLang === 'ar' ? subItem.name : subItem.nameEn}\n                        </Link>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Search Button */}\n            <button\n              className={`p-2 rounded-lg transition-colors ${\n                isScrolled\n                  ? 'text-gray-600 hover:text-sky-600 hover:bg-sky-50'\n                  : 'text-white hover:text-sky-200 hover:bg-white/10'\n              }`}\n            >\n              <Search className=\"w-5 h-5\" />\n            </button>\n\n            {/* Language Toggle */}\n            <button\n              onClick={toggleLanguage}\n              className={`p-2 rounded-lg transition-colors ${\n                isScrolled\n                  ? 'text-gray-600 hover:text-sky-600 hover:bg-sky-50'\n                  : 'text-white hover:text-sky-200 hover:bg-white/10'\n              }`}\n            >\n              <Globe className=\"w-5 h-5\" />\n            </button>\n\n            {/* Dark Mode Toggle */}\n            <button\n              onClick={toggleDarkMode}\n              className={`p-2 rounded-lg transition-colors ${\n                isScrolled\n                  ? 'text-gray-600 hover:text-sky-600 hover:bg-sky-50'\n                  : 'text-white hover:text-sky-200 hover:bg-white/10'\n              }`}\n            >\n              {isDarkMode ? <Sun className=\"w-5 h-5\" /> : <Moon className=\"w-5 h-5\" />}\n            </button>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className={`lg:hidden p-2 rounded-lg transition-colors ${\n                isScrolled\n                  ? 'text-gray-600 hover:text-sky-600 hover:bg-sky-50'\n                  : 'text-white hover:text-sky-200 hover:bg-white/10'\n              }`}\n            >\n              {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"lg:hidden bg-white border-t border-gray-200 shadow-lg\"\n          >\n            <div className=\"container mx-auto px-4 py-4\">\n              <nav className=\"space-y-2\">\n                {navItems.map((item, index) => (\n                  <div key={index}>\n                    <Link\n                      href={item.href}\n                      className=\"flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg text-gray-700 hover:bg-sky-50 hover:text-sky-600 transition-colors\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span>{currentLang === 'ar' ? item.name : item.nameEn}</span>\n                    </Link>\n\n                    {/* Mobile Submenu */}\n                    {item.submenu && (\n                      <div className=\"mr-8 mt-2 space-y-1\">\n                        {item.submenu.map((subItem, subIndex) => (\n                          <Link\n                            key={subIndex}\n                            href={subItem.href}\n                            className=\"block px-4 py-2 text-sm text-gray-600 hover:text-sky-600 transition-colors\"\n                            onClick={() => setIsMenuOpen(false)}\n                          >\n                            {currentLang === 'ar' ? subItem.name : subItem.nameEn}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </nav>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Ad Space - Hidden by default, will be shown when ads are added */}\n      <div className=\"hidden\" id=\"header-ad-space\">\n        {/* This space will be used for AdSense ads later */}\n        <div className=\"bg-gray-100 h-16 flex items-center justify-center text-gray-500 text-sm\">\n          مساحة إعلانية\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,cAAc,CAAC;QACf,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;IAC5C;IAEA,MAAM,iBAAiB;QACrB,eAAe,gBAAgB,OAAO,OAAO;IAC7C,2DAA2D;IAC7D;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,SAAS;gBACP;oBAAE,MAAM;oBAAwB,QAAQ;oBAAiB,MAAM;gBAAsB;gBACrF;oBAAE,MAAM;oBAAmB,QAAQ;oBAAe,MAAM;gBAAoB;gBAC5E;oBAAE,MAAM;oBAAmB,QAAQ;oBAAa,MAAM;gBAAS;aAChE;QACH;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,SAAS;gBACP;oBAAE,MAAM;oBAAsB,QAAQ;oBAAW,MAAM;gBAAwB;gBAC/E;oBAAE,MAAM;oBAAoB,QAAQ;oBAAU,MAAM;gBAAuB;gBAC3E;oBAAE,MAAM;oBAAoB,QAAQ;oBAAe,MAAM;gBAAqB;aAC/E;QACH;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;KACD;IAED,qBACE,8OAAC;QACC,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,aACI,qDACA,mDACJ;;8DAEF,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;8DAAM,gBAAgB,OAAO,KAAK,IAAI,GAAG,KAAK,MAAM;;;;;;gDACpD,KAAK,OAAO,kBAAI,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;wCAIzC,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,yBAC1B,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,QAAQ,IAAI;wDAClB,WAAU;kEAET,gBAAgB,OAAO,QAAQ,IAAI,GAAG,QAAQ,MAAM;uDAJhD;;;;;;;;;;;;;;;;mCApBP;;;;;;;;;;sCAmCd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,WAAW,CAAC,iCAAiC,EAC3C,aACI,qDACA,mDACJ;8CAEF,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,iCAAiC,EAC3C,aACI,qDACA,mDACJ;8CAEF,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAInB,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,iCAAiC,EAC3C,aACI,qDACA,mDACJ;8CAED,2BAAa,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAI9D,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAW,CAAC,2CAA2C,EACrD,aACI,qDACA,mDACJ;8CAED,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;;sDACC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;8DAAM,gBAAgB,OAAO,KAAK,IAAI,GAAG,KAAK,MAAM;;;;;;;;;;;;wCAItD,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,yBAC1B,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,QAAQ,IAAI;oDAClB,WAAU;oDACV,SAAS,IAAM,cAAc;8DAE5B,gBAAgB,OAAO,QAAQ,IAAI,GAAG,QAAQ,MAAM;mDALhD;;;;;;;;;;;mCAfL;;;;;;;;;;;;;;;;;;;;;;;;;0BAkCtB,8OAAC;gBAAI,WAAU;gBAAS,IAAG;0BAEzB,cAAA,8OAAC;oBAAI,WAAU;8BAA0E;;;;;;;;;;;;;;;;;AAMjG", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/444/mwady-net/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { motion } from 'framer-motion'\nimport { \n  BookOpen, \n  Mail, \n  Phone, \n  MapPin, \n  Facebook, \n  Twitter, \n  Instagram, \n  Youtube,\n  Heart,\n  ExternalLink\n} from 'lucide-react'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerSections = [\n    {\n      title: 'روابط سريعة',\n      links: [\n        { name: 'الرئيسية', href: '/' },\n        { name: 'اختبارات منتصف الفصل', href: '/exams?type=midterm' },\n        { name: 'اختبارات نهائية', href: '/exams?type=final' },\n        { name: 'المراحل التعليمية', href: '/grades' },\n        { name: 'للمعلمين', href: '/teachers' }\n      ]\n    },\n    {\n      title: 'المراحل التعليمية',\n      links: [\n        { name: 'المرحلة الابتدائية', href: '/grades?level=primary' },\n        { name: 'المرحلة المتوسطة', href: '/grades?level=middle' },\n        { name: 'المرحلة الثانوية', href: '/grades?level=high' },\n        { name: 'جميع الصفوف', href: '/grades' }\n      ]\n    },\n    {\n      title: 'الدعم والمساعدة',\n      links: [\n        { name: 'اتصل بنا', href: '/contact' },\n        { name: 'الأسئلة الشائعة', href: '/faq' },\n        { name: 'سياسة الخصوصية', href: '/privacy' },\n        { name: 'شروط الاستخدام', href: '/terms' },\n        { name: 'تقرير مشكلة', href: '/report' }\n      ]\n    }\n  ]\n\n  const socialLinks = [\n    { name: 'Facebook', icon: Facebook, href: 'https://facebook.com/mwady.net', color: 'hover:text-blue-600' },\n    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/mwady_net', color: 'hover:text-blue-400' },\n    { name: 'Instagram', icon: Instagram, href: 'https://instagram.com/mwady_net', color: 'hover:text-pink-600' },\n    { name: 'YouTube', icon: Youtube, href: 'https://youtube.com/@mwady_net', color: 'hover:text-red-600' }\n  ]\n\n  const educationalPartners = [\n    { name: 'وزارة التعليم', href: 'https://moe.gov.sa' },\n    { name: 'منصة مدرستي', href: 'https://schools.madrasati.sa' },\n    { name: 'بوابة عين', href: 'https://ien.edu.sa' },\n    { name: 'نور', href: 'https://noor.moe.gov.sa' }\n  ]\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Ad Space - Hidden by default */}\n      <div className=\"hidden\" id=\"footer-ad-space-top\">\n        <div className=\"bg-gray-800 py-4\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"bg-gray-700 h-24 rounded-lg flex items-center justify-center text-gray-400 text-sm\">\n              مساحة إعلانية\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <Link href=\"/\" className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                  <BookOpen className=\"w-7 h-7 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">موقع موادي</h3>\n                  <p className=\"text-sm text-gray-400 font-mono\">mwady.net</p>\n                </div>\n              </Link>\n              \n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                الموقع الأول لتحضير الاختبارات في التعليم السعودي. منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 space-x-reverse text-gray-300\">\n                  <Mail className=\"w-4 h-4 text-purple-400\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 space-x-reverse text-gray-300\">\n                  <MapPin className=\"w-4 h-4 text-purple-400\" />\n                  <span className=\"text-sm\">المملكة العربية السعودية</span>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Sections */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <h4 className=\"text-lg font-semibold mb-6 text-white\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link, linkIndex) => (\n                  <li key={linkIndex}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-300 hover:text-purple-400 transition-colors text-sm flex items-center space-x-2 space-x-reverse\"\n                    >\n                      <span>{link.name}</span>\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Educational Partners */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mt-12 pt-8 border-t border-gray-700\"\n        >\n          <h4 className=\"text-lg font-semibold mb-6 text-center\">شركاؤنا التعليميون</h4>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            {educationalPartners.map((partner, index) => (\n              <Link\n                key={index}\n                href={partner.href}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"bg-gray-800 rounded-lg p-4 text-center hover:bg-gray-700 transition-colors group\"\n              >\n                <span className=\"text-sm text-gray-300 group-hover:text-white flex items-center justify-center space-x-2 space-x-reverse\">\n                  <span>{partner.name}</span>\n                  <ExternalLink className=\"w-3 h-3\" />\n                </span>\n              </Link>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Social Media & Newsletter */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          className=\"mt-12 pt-8 border-t border-gray-700\"\n        >\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-6 space-x-reverse mb-6 md:mb-0\">\n              <span className=\"text-gray-300 text-sm\">تابعنا على:</span>\n              {socialLinks.map((social, index) => (\n                <Link\n                  key={index}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className={`text-gray-400 ${social.color} transition-colors`}\n                  aria-label={social.name}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </Link>\n              ))}\n            </div>\n\n            {/* Newsletter Signup */}\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <input\n                type=\"email\"\n                placeholder=\"اشترك في النشرة البريدية\"\n                className=\"bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\"\n                dir=\"rtl\"\n              />\n              <button className=\"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg text-sm transition-colors\">\n                اشتراك\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"bg-gray-800 border-t border-gray-700\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between text-sm text-gray-400\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4 md:mb-0\">\n              <span>© {currentYear} موقع موادي. جميع الحقوق محفوظة.</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <span>صُنع بـ</span>\n              <Heart className=\"w-4 h-4 text-red-500\" />\n              <span>في المملكة العربية السعودية</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Ad Space - Hidden by default */}\n      <div className=\"hidden\" id=\"footer-ad-space-bottom\">\n        <div className=\"bg-gray-800 py-4\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"bg-gray-700 h-16 rounded-lg flex items-center justify-center text-gray-400 text-sm\">\n              مساحة إعلانية\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAI;gBAC9B;oBAAE,MAAM;oBAAwB,MAAM;gBAAsB;gBAC5D;oBAAE,MAAM;oBAAmB,MAAM;gBAAoB;gBACrD;oBAAE,MAAM;oBAAqB,MAAM;gBAAU;gBAC7C;oBAAE,MAAM;oBAAY,MAAM;gBAAY;aACvC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAwB;gBAC5D;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAoB,MAAM;gBAAqB;gBACvD;oBAAE,MAAM;oBAAe,MAAM;gBAAU;aACxC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAW;gBACrC;oBAAE,MAAM;oBAAmB,MAAM;gBAAO;gBACxC;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAkB,MAAM;gBAAS;gBACzC;oBAAE,MAAM;oBAAe,MAAM;gBAAU;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAkC,OAAO;QAAsB;QACzG;YAAE,MAAM;YAAW,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAiC,OAAO;QAAsB;QACtG;YAAE,MAAM;YAAa,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAmC,OAAO;QAAsB;QAC5G;YAAE,MAAM;YAAW,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAkC,OAAO;QAAqB;KACvG;IAED,MAAM,sBAAsB;QAC1B;YAAE,MAAM;YAAiB,MAAM;QAAqB;QACpD;YAAE,MAAM;YAAe,MAAM;QAA+B;QAC5D;YAAE,MAAM;YAAa,MAAM;QAAqB;QAChD;YAAE,MAAM;YAAO,MAAM;QAA0B;KAChD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;gBAAS,IAAG;0BACzB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAqF;;;;;;;;;;;;;;;;;;;;;0BAQ1G,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoB;;;;;;sEAClC,8OAAC;4DAAE,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAInD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAKlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOjC,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;;sDAEhD,8OAAC;4CAAG,WAAU;sDAAyC,QAAQ,KAAK;;;;;;sDACpE,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAEV,cAAA,8OAAC;sEAAM,KAAK,IAAI;;;;;;;;;;;mDALX;;;;;;;;;;;mCARR;;;;;;;;;;;kCAuBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC,SAAS,sBACjC,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,QAAQ,IAAI;wCAClB,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;8DAAM,QAAQ,IAAI;;;;;;8DACnB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;uCARrB;;;;;;;;;;;;;;;;kCAgBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;wCACvC,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,WAAW,CAAC,cAAc,EAAE,OAAO,KAAK,CAAC,kBAAkB,CAAC;gDAC5D,cAAY,OAAO,IAAI;0DAEvB,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;+CAPlB;;;;;;;;;;;8CAaX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,KAAI;;;;;;sDAEN,8OAAC;4CAAO,WAAU;sDAA8F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCAAK;wCAAG;wCAAY;;;;;;;;;;;;0CAGvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;gBAAS,IAAG;0BACzB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAqF;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhH", "debugId": null}}]}