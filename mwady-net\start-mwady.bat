@echo off
chcp 65001 >nul
title موقع موادي - mwady.net

echo.
echo ========================================
echo    موقع موادي - mwady.net
echo    الموقع الأول لتحضير الاختبارات
echo    في التعليم السعودي
echo ========================================
echo.

:: التحقق من وجود Node.js
echo [1/5] التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت!
    echo.
    echo يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org
    echo.
    pause
    exit /b 1
)
echo ✅ Node.js مثبت بنجاح

:: التحقق من وجود npm
echo [2/5] التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر!
    pause
    exit /b 1
)
echo ✅ npm متوفر

:: التحقق من وجود المكتبات
echo [3/5] التحقق من المكتبات...
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    echo هذا قد يستغرق بضع دقائق في المرة الأولى...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت المكتبات!
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة مسبقاً
)

:: بناء المشروع إذا لزم الأمر
echo [4/5] إعداد المشروع...
if not exist ".next" (
    echo 🔨 بناء المشروع...
    npm run build
    if %errorlevel% neq 0 (
        echo ⚠️ تحذير: فشل في بناء المشروع، سيتم تشغيل وضع التطوير
    )
)

:: تشغيل المشروع
echo [5/5] تشغيل الموقع...
echo.
echo 🚀 جاري تشغيل موقع موادي...
echo.
echo سيتم فتح الموقع في المتصفح تلقائياً على:
echo http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo ========================================
echo.

:: تشغيل الخادم
start "" "http://localhost:3000"
npm run dev

:: في حالة الخطأ
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل الموقع!
    echo.
    echo الحلول المقترحة:
    echo 1. تأكد من أن المنفذ 3000 غير مستخدم
    echo 2. أعد تشغيل الملف كمدير
    echo 3. احذف مجلد node_modules وأعد تشغيل الملف
    echo.
    pause
)
