@echo off
chcp 65001 >nul
title Mwady.net - Start Project

:: Change to the directory where this batch file is located
cd /d "%~dp0"

echo.
echo ========================================
echo    Mwady.net - Start Project
echo    Saudi Education Exam Platform
echo ========================================
echo.
echo Current directory: %CD%
echo.

echo [1/3] Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please download and install Node.js from: https://nodejs.org
    pause
    exit /b 1
)
echo OK: Node.js is installed

echo [2/3] Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes on first run...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
    echo OK: Dependencies installed successfully
) else (
    echo OK: Dependencies already installed
)

echo [3/3] Starting website...
echo.
echo Starting Mwady.net...
echo.
echo Website will open automatically at:
echo http://localhost:3000
echo.
echo To stop: Press Ctrl+C
echo ========================================
echo.

start "" "http://localhost:3000"
npm run dev

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start the website!
    echo.
    echo Suggested solutions:
    echo 1. Make sure port 3000 is not in use
    echo 2. Run this file as administrator
    echo 3. Delete node_modules folder and run again
    echo.
    pause
)
