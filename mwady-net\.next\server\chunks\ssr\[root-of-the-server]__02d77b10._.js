module.exports = {

"[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inter_9e72d27f-module__JKMi0a__className",
  "variable": "inter_9e72d27f-module__JKMi0a__variable",
});
}}),
"[next]/internal/font/google/inter_9e72d27f.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_9e72d27f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_9e72d27f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_9e72d27f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_9e72d27f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/cairo_ce960574.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "cairo_ce960574-module__O9vAua__className",
  "variable": "cairo_ce960574-module__O9vAua__variable",
});
}}),
"[next]/internal/font/google/cairo_ce960574.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$cairo_ce960574$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/cairo_ce960574.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$cairo_ce960574$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Cairo', 'Cairo Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$cairo_ce960574$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$cairo_ce960574$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/components/Header.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/Header.tsx <module evaluation>", "default");
}}),
"[project]/src/components/Header.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/Header.tsx", "default");
}}),
"[project]/src/components/Header.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/Header.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/Header.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/Footer.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/Footer.tsx <module evaluation>", "default");
}}),
"[project]/src/components/Footer.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/Footer.tsx", "default");
}}),
"[project]/src/components/Footer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Footer$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/Footer.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Footer$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/Footer.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Footer$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/lib/seo-keywords.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// SEO Keywords for Saudi Educational Content
__turbopack_context__.s({
    "generateMetaKeywords": (()=>generateMetaKeywords),
    "generateStructuredData": (()=>generateStructuredData),
    "seoKeywords": (()=>seoKeywords)
});
const seoKeywords = {
    // Primary Keywords (High Volume)
    primary: [
        'اختبارات منتصف الفصل',
        'اختبارات نهائية',
        'نماذج اختبارات',
        'بنك الاختبارات',
        'اختبارات تجريبية',
        'نماذج امتحانات',
        'اختبارات السعودية',
        'التعليم السعودي',
        'مناهج السعودية',
        'وزارة التعليم'
    ],
    // Secondary Keywords (Medium Volume)
    secondary: [
        'اختبارات ابتدائي',
        'اختبارات متوسط',
        'اختبارات ثانوي',
        'اختبارات الفصل الأول',
        'اختبارات الفصل الثاني',
        'اختبارات الفصل الثالث',
        'حلول الاختبارات',
        'مراجعة نهائية',
        'تحضير الاختبارات',
        'دراسة الامتحانات'
    ],
    // Subject-Specific Keywords
    subjects: [
        'اختبارات رياضيات',
        'اختبارات علوم',
        'اختبارات لغتي',
        'اختبارات انجليزي',
        'اختبارات اجتماعيات',
        'اختبارات دراسات اسلامية',
        'اختبارات تجويد',
        'اختبارات مهارات رقمية',
        'اختبارات تربية فنية',
        'اختبارات تربية بدنية'
    ],
    // Grade-Specific Keywords
    grades: [
        'اختبارات أول ابتدائي',
        'اختبارات ثاني ابتدائي',
        'اختبارات ثالث ابتدائي',
        'اختبارات رابع ابتدائي',
        'اختبارات خامس ابتدائي',
        'اختبارات سادس ابتدائي',
        'اختبارات أول متوسط',
        'اختبارات ثاني متوسط',
        'اختبارات ثالث متوسط',
        'اختبارات أول ثانوي',
        'اختبارات ثاني ثانوي',
        'اختبارات ثالث ثانوي'
    ],
    // Long-tail Keywords (Low Competition, High Intent)
    longTail: [
        'تحميل اختبارات منتصف الفصل pdf',
        'نماذج اختبارات نهائية مع الحلول',
        'بنك اختبارات وزارة التعليم السعودية',
        'اختبارات تجريبية للمرحلة الابتدائية',
        'نماذج امتحانات المرحلة المتوسطة',
        'اختبارات نهائية المرحلة الثانوية',
        'مراجعة شاملة قبل الاختبارات',
        'تحضير الطلاب للامتحانات النهائية',
        'نصائح للنجاح في الاختبارات',
        'استراتيجيات حل الاختبارات'
    ],
    // Trending Keywords (Social Media & YouTube)
    trending: [
        'اختبارات تيك توك',
        'مراجعة يوتيوب',
        'شرح الاختبارات',
        'حل نماذج الامتحانات',
        'تجميعات الاختبارات',
        'ملخصات المراجعة',
        'خرائط ذهنية',
        'فلاش كاردز',
        'مذاكرة جماعية',
        'دراسة اونلاين'
    ],
    // Location-Based Keywords
    locations: [
        'اختبارات الرياض',
        'اختبارات جدة',
        'اختبارات الدمام',
        'اختبارات مكة',
        'اختبارات المدينة',
        'اختبارات الطائف',
        'اختبارات القصيم',
        'اختبارات حائل',
        'اختبارات جازان',
        'اختبارات نجران'
    ],
    // Exam Types
    examTypes: [
        'اختبار شفهي',
        'اختبار كتابي',
        'اختبار عملي',
        'اختبار الكتروني',
        'اختبار تفاعلي',
        'اختبار محوسب',
        'اختبار ورقي',
        'اختبار مركزي',
        'اختبار موحد',
        'اختبار تشخيصي'
    ],
    // Academic Year Keywords
    academicYear: [
        'العام الدراسي 1446',
        'الفصل الدراسي الأول 1446',
        'الفصل الدراسي الثاني 1446',
        'الفصل الدراسي الثالث 1446',
        'منهج جديد 1446',
        'تحديث المناهج',
        'طبعة جديدة',
        'منهج محدث',
        'كتب جديدة',
        'مقررات حديثة'
    ]
};
const generateMetaKeywords = (grade, subject, examType, semester)=>{
    let keywords = [
        ...seoKeywords.primary
    ];
    if (grade) {
        keywords.push(`اختبارات ${grade}`);
    }
    if (subject) {
        keywords.push(`اختبارات ${subject}`);
    }
    if (examType === 'midterm') {
        keywords.push('اختبارات منتصف الفصل');
    } else if (examType === 'final') {
        keywords.push('اختبارات نهائية');
    }
    if (semester) {
        keywords.push(`الفصل الدراسي ${semester === 1 ? 'الأول' : semester === 2 ? 'الثاني' : 'الثالث'}`);
    }
    // Add some secondary and long-tail keywords
    keywords.push(...seoKeywords.secondary.slice(0, 5));
    keywords.push(...seoKeywords.longTail.slice(0, 3));
    return keywords.join(', ');
};
const generateStructuredData = (title, description, url, imageUrl)=>{
    return {
        '@context': 'https://schema.org',
        '@type': 'EducationalOrganization',
        name: 'موقع موادي - mwady.net',
        description: 'الموقع الأول لتحضير الاختبارات في التعليم السعودي',
        url: 'https://mwady.net',
        logo: 'https://mwady.net/logo.png',
        sameAs: [
            'https://twitter.com/mwady_net',
            'https://facebook.com/mwady.net',
            'https://instagram.com/mwady_net'
        ],
        mainEntity: {
            '@type': 'WebPage',
            name: title,
            description: description,
            url: url,
            image: imageUrl,
            publisher: {
                '@type': 'Organization',
                name: 'موقع موادي',
                logo: 'https://mwady.net/logo.png'
            }
        }
    };
};
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_9e72d27f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_9e72d27f.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$cairo_ce960574$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/cairo_ce960574.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Header.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Footer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$keywords$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/seo-keywords.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const metadata = {
    title: "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
    description: "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية - اختبارات منتصف الفصل والاختبارات النهائية للتعليم السعودي",
    keywords: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$keywords$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateMetaKeywords"])(),
    authors: [
        {
            name: "موقع موادي"
        }
    ],
    creator: "موقع موادي",
    publisher: "موقع موادي",
    robots: "index, follow",
    openGraph: {
        title: "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
        description: "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية",
        url: "https://mwady.net",
        siteName: "موقع موادي",
        locale: "ar_SA",
        type: "website",
        images: [
            {
                url: "https://mwady.net/og-image.jpg",
                width: 1200,
                height: 630,
                alt: "موقع موادي - اختبارات التعليم السعودي"
            }
        ]
    },
    twitter: {
        card: "summary_large_image",
        title: "موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي",
        description: "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية",
        images: [
            "https://mwady.net/og-image.jpg"
        ]
    },
    alternates: {
        canonical: "https://mwady.net",
        languages: {
            "ar-SA": "https://mwady.net",
            "en-US": "https://mwady.net/en"
        }
    }
};
function RootLayout({ children }) {
    const structuredData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$keywords$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateStructuredData"])("موقع موادي - الموقع الأول لتحضير الاختبارات في التعليم السعودي", "منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية", "https://mwady.net");
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "ar",
        dir: "rtl",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify(structuredData)
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 71,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "icon",
                        href: "/favicon.ico"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "apple-touch-icon",
                        href: "/apple-touch-icon.png"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "theme-color",
                        content: "#7c3aed"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 77,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "msapplication-TileColor",
                        content: "#7c3aed"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 70,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_9e72d27f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$cairo_ce960574$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} font-cairo antialiased`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 82,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 80,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__02d77b10._.js.map