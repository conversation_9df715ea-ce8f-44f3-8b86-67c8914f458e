-- Database setup for <PERSON>wady.net
-- Run this SQL in your Supabase SQL editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create grades table
CREATE TABLE IF NOT EXISTS grades (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name_ar TEXT NOT NULL,
    name_en TEXT NOT NULL,
    level TEXT NOT NULL CHECK (level IN ('primary', 'middle', 'high')),
    order_num INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subjects table
CREATE TABLE IF NOT EXISTS subjects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name_ar TEXT NOT NULL,
    name_en TEXT NOT NULL,
    grade_id UUID REFERENCES grades(id) ON DELETE CASCADE,
    icon TEXT,
    color TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create teachers table
CREATE TABLE IF NOT EXISTS teachers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    school TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create exams table
CREATE TABLE IF NOT EXISTS exams (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title_ar TEXT NOT NULL,
    title_en TEXT,
    description_ar TEXT,
    description_en TEXT,
    subject_id UUID REFERENCES subjects(id) ON DELETE CASCADE,
    grade_id UUID REFERENCES grades(id) ON DELETE CASCADE,
    semester INTEGER CHECK (semester IN (1, 2, 3)),
    exam_type TEXT CHECK (exam_type IN ('midterm', 'final')),
    file_url TEXT NOT NULL,
    file_size INTEGER,
    download_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES teachers(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to increment download count
CREATE OR REPLACE FUNCTION increment_download_count(exam_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE exams 
    SET download_count = download_count + 1 
    WHERE id = exam_id;
END;
$$ LANGUAGE plpgsql;

-- Insert sample grades data
INSERT INTO grades (name_ar, name_en, level, order_num) VALUES
('الأول الابتدائي', 'First Grade', 'primary', 1),
('الثاني الابتدائي', 'Second Grade', 'primary', 2),
('الثالث الابتدائي', 'Third Grade', 'primary', 3),
('الرابع الابتدائي', 'Fourth Grade', 'primary', 4),
('الخامس الابتدائي', 'Fifth Grade', 'primary', 5),
('السادس الابتدائي', 'Sixth Grade', 'primary', 6),
('الأول المتوسط', 'Seventh Grade', 'middle', 7),
('الثاني المتوسط', 'Eighth Grade', 'middle', 8),
('الثالث المتوسط', 'Ninth Grade', 'middle', 9),
('الأول الثانوي', 'Tenth Grade', 'high', 10),
('الثاني الثانوي', 'Eleventh Grade', 'high', 11),
('الثالث الثانوي', 'Twelfth Grade', 'high', 12)
ON CONFLICT DO NOTHING;

-- Insert sample subjects for each grade
DO $$
DECLARE
    grade_record RECORD;
    subject_names TEXT[][];
BEGIN
    -- Primary subjects
    subject_names := ARRAY[
        ['لغتي', 'Arabic Language'],
        ['الرياضيات', 'Mathematics'],
        ['العلوم', 'Science'],
        ['التربية الإسلامية', 'Islamic Education'],
        ['التربية الفنية', 'Art Education'],
        ['التربية البدنية', 'Physical Education']
    ];
    
    FOR grade_record IN SELECT id FROM grades WHERE level = 'primary' LOOP
        FOR i IN 1..array_length(subject_names, 1) LOOP
            INSERT INTO subjects (name_ar, name_en, grade_id) 
            VALUES (subject_names[i][1], subject_names[i][2], grade_record.id)
            ON CONFLICT DO NOTHING;
        END LOOP;
    END LOOP;
    
    -- Middle school subjects
    subject_names := ARRAY[
        ['لغتي', 'Arabic Language'],
        ['الرياضيات', 'Mathematics'],
        ['العلوم', 'Science'],
        ['الإنجليزي', 'English'],
        ['الاجتماعيات', 'Social Studies'],
        ['التربية الإسلامية', 'Islamic Education'],
        ['التربية الفنية', 'Art Education'],
        ['التربية البدنية', 'Physical Education'],
        ['المهارات الرقمية', 'Digital Skills']
    ];
    
    FOR grade_record IN SELECT id FROM grades WHERE level = 'middle' LOOP
        FOR i IN 1..array_length(subject_names, 1) LOOP
            INSERT INTO subjects (name_ar, name_en, grade_id) 
            VALUES (subject_names[i][1], subject_names[i][2], grade_record.id)
            ON CONFLICT DO NOTHING;
        END LOOP;
    END LOOP;
    
    -- High school subjects
    subject_names := ARRAY[
        ['اللغة العربية', 'Arabic Language'],
        ['الرياضيات', 'Mathematics'],
        ['الفيزياء', 'Physics'],
        ['الكيمياء', 'Chemistry'],
        ['الأحياء', 'Biology'],
        ['الإنجليزي', 'English'],
        ['التاريخ', 'History'],
        ['الجغرافيا', 'Geography'],
        ['التربية الإسلامية', 'Islamic Education'],
        ['علوم الحاسب', 'Computer Science']
    ];
    
    FOR grade_record IN SELECT id FROM grades WHERE level = 'high' LOOP
        FOR i IN 1..array_length(subject_names, 1) LOOP
            INSERT INTO subjects (name_ar, name_en, grade_id) 
            VALUES (subject_names[i][1], subject_names[i][2], grade_record.id)
            ON CONFLICT DO NOTHING;
        END LOOP;
    END LOOP;
END $$;

-- Enable Row Level Security
ALTER TABLE grades ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE exams ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Allow public read access on grades" ON grades FOR SELECT USING (true);
CREATE POLICY "Allow public read access on subjects" ON subjects FOR SELECT USING (true);
CREATE POLICY "Allow public read access on exams" ON exams FOR SELECT USING (is_active = true);

-- Create policies for teachers
CREATE POLICY "Allow teachers to read their own data" ON teachers FOR SELECT USING (true);
CREATE POLICY "Allow teachers to insert their own data" ON teachers FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow teachers to update their own data" ON teachers FOR UPDATE USING (true);

-- Create policies for exam management
CREATE POLICY "Allow teachers to insert exams" ON exams FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow teachers to update their own exams" ON exams FOR UPDATE USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_grades_level ON grades(level);
CREATE INDEX IF NOT EXISTS idx_grades_order ON grades(order_num);
CREATE INDEX IF NOT EXISTS idx_subjects_grade_id ON subjects(grade_id);
CREATE INDEX IF NOT EXISTS idx_exams_grade_id ON exams(grade_id);
CREATE INDEX IF NOT EXISTS idx_exams_subject_id ON exams(subject_id);
CREATE INDEX IF NOT EXISTS idx_exams_semester ON exams(semester);
CREATE INDEX IF NOT EXISTS idx_exams_exam_type ON exams(exam_type);
CREATE INDEX IF NOT EXISTS idx_exams_is_active ON exams(is_active);
CREATE INDEX IF NOT EXISTS idx_exams_created_at ON exams(created_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_exams_updated_at 
    BEFORE UPDATE ON exams 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
