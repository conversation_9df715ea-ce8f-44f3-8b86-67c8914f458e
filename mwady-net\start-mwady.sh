#!/bin/bash

echo ""
echo "========================================"
echo "   Mwady.net - Start Project"
echo "   Saudi Education Exam Platform"
echo "========================================"
echo ""
echo "Current directory: $(pwd)"
echo ""

echo "[1/3] Checking Node.js..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed!"
    echo "Please download and install Node.js from: https://nodejs.org"
    read -p "Press any key to continue..."
    exit 1
fi
echo "OK: Node.js is installed ($(node --version))"

echo "[2/3] Checking dependencies..."
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    echo "This may take a few minutes on first run..."
    npm install
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to install dependencies!"
        read -p "Press any key to continue..."
        exit 1
    fi
    echo "OK: Dependencies installed successfully"
else
    echo "OK: Dependencies already installed"
fi

echo "[3/3] Starting website..."
echo ""
echo "Starting Mwady.net..."
echo ""
echo "Website will open automatically at:"
echo "http://localhost:3000"
echo ""
echo "To stop: Press Ctrl+C"
echo "========================================"
echo ""

# Try to open browser (works on Windows with WSL)
if command -v cmd.exe &> /dev/null; then
    cmd.exe /c start http://localhost:3000 2>/dev/null &
elif command -v explorer.exe &> /dev/null; then
    explorer.exe http://localhost:3000 2>/dev/null &
fi

# Start the development server
npm run dev

if [ $? -ne 0 ]; then
    echo ""
    echo "ERROR: Failed to start the website!"
    echo ""
    echo "Suggested solutions:"
    echo "1. Make sure port 3000 is not in use"
    echo "2. Delete node_modules folder and run again"
    echo "3. Check if all dependencies are installed"
    echo ""
    read -p "Press any key to continue..."
fi
