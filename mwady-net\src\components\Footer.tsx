'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  BookOpen, 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Youtube,
  Heart,
  ExternalLink
} from 'lucide-react'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const footerSections = [
    {
      title: 'روابط سريعة',
      links: [
        { name: 'الرئيسية', href: '/' },
        { name: 'اختبارات منتصف الفصل', href: '/exams?type=midterm' },
        { name: 'اختبارات نهائية', href: '/exams?type=final' },
        { name: 'المراحل التعليمية', href: '/grades' },
        { name: 'للمعلمين', href: '/teachers' }
      ]
    },
    {
      title: 'المراحل التعليمية',
      links: [
        { name: 'المرحلة الابتدائية', href: '/grades?level=primary' },
        { name: 'المرحلة المتوسطة', href: '/grades?level=middle' },
        { name: 'المرحلة الثانوية', href: '/grades?level=high' },
        { name: 'جميع الصفوف', href: '/grades' }
      ]
    },
    {
      title: 'الدعم والمساعدة',
      links: [
        { name: 'اتصل بنا', href: '/contact' },
        { name: 'الأسئلة الشائعة', href: '/faq' },
        { name: 'سياسة الخصوصية', href: '/privacy' },
        { name: 'شروط الاستخدام', href: '/terms' },
        { name: 'تقرير مشكلة', href: '/report' }
      ]
    }
  ]

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: 'https://facebook.com/mwady.net', color: 'hover:text-blue-600' },
    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/mwady_net', color: 'hover:text-blue-400' },
    { name: 'Instagram', icon: Instagram, href: 'https://instagram.com/mwady_net', color: 'hover:text-pink-600' },
    { name: 'YouTube', icon: Youtube, href: 'https://youtube.com/@mwady_net', color: 'hover:text-red-600' }
  ]

  const educationalPartners = [
    { name: 'وزارة التعليم', href: 'https://moe.gov.sa' },
    { name: 'منصة مدرستي', href: 'https://schools.madrasati.sa' },
    { name: 'بوابة عين', href: 'https://ien.edu.sa' },
    { name: 'نور', href: 'https://noor.moe.gov.sa' }
  ]

  return (
    <footer className="bg-gray-900 text-white">
      {/* Ad Space - Hidden by default */}
      <div className="hidden" id="footer-ad-space-top">
        <div className="bg-gray-800 py-4">
          <div className="container mx-auto px-4">
            <div className="bg-gray-700 h-24 rounded-lg flex items-center justify-center text-gray-400 text-sm">
              مساحة إعلانية
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Link href="/" className="flex items-center space-x-3 space-x-reverse mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <BookOpen className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">موقع موادي</h3>
                  <p className="text-sm text-gray-400 font-mono">mwady.net</p>
                </div>
              </Link>
              
              <p className="text-gray-300 mb-6 leading-relaxed">
                الموقع الأول لتحضير الاختبارات في التعليم السعودي. منصة شاملة تضم آلاف نماذج الاختبارات لجميع المراحل الدراسية.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                  <Mail className="w-4 h-4 text-purple-400" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                  <MapPin className="w-4 h-4 text-purple-400" />
                  <span className="text-sm">المملكة العربية السعودية</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <h4 className="text-lg font-semibold mb-6 text-white">{section.title}</h4>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-purple-400 transition-colors text-sm flex items-center space-x-2 space-x-reverse"
                    >
                      <span>{link.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Educational Partners */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-12 pt-8 border-t border-gray-700"
        >
          <h4 className="text-lg font-semibold mb-6 text-center">شركاؤنا التعليميون</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {educationalPartners.map((partner, index) => (
              <Link
                key={index}
                href={partner.href}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gray-800 rounded-lg p-4 text-center hover:bg-gray-700 transition-colors group"
              >
                <span className="text-sm text-gray-300 group-hover:text-white flex items-center justify-center space-x-2 space-x-reverse">
                  <span>{partner.name}</span>
                  <ExternalLink className="w-3 h-3" />
                </span>
              </Link>
            ))}
          </div>
        </motion.div>

        {/* Social Media & Newsletter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-12 pt-8 border-t border-gray-700"
        >
          <div className="flex flex-col md:flex-row items-center justify-between">
            {/* Social Links */}
            <div className="flex items-center space-x-6 space-x-reverse mb-6 md:mb-0">
              <span className="text-gray-300 text-sm">تابعنا على:</span>
              {socialLinks.map((social, index) => (
                <Link
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-400 ${social.color} transition-colors`}
                  aria-label={social.name}
                >
                  <social.icon className="w-5 h-5" />
                </Link>
              ))}
            </div>

            {/* Newsletter Signup */}
            <div className="flex items-center space-x-3 space-x-reverse">
              <input
                type="email"
                placeholder="اشترك في النشرة البريدية"
                className="bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                dir="rtl"
              />
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg text-sm transition-colors">
                اشتراك
              </button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-gray-800 border-t border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between text-sm text-gray-400">
            <div className="flex items-center space-x-2 space-x-reverse mb-4 md:mb-0">
              <span>© {currentYear} موقع موادي. جميع الحقوق محفوظة.</span>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <span>صُنع بـ</span>
              <Heart className="w-4 h-4 text-red-500" />
              <span>في المملكة العربية السعودية</span>
            </div>
          </div>
        </div>
      </div>

      {/* Ad Space - Hidden by default */}
      <div className="hidden" id="footer-ad-space-bottom">
        <div className="bg-gray-800 py-4">
          <div className="container mx-auto px-4">
            <div className="bg-gray-700 h-16 rounded-lg flex items-center justify-center text-gray-400 text-sm">
              مساحة إعلانية
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
